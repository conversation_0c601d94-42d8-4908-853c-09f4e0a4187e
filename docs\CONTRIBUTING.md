# Contributing to <PERSON><PERSON>al

Thank you for your interest in contributing to Focal! This document provides guidelines and instructions for contributing.

## Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/your-username/Focal.git`
3. Create a feature branch: `git checkout -b feature/amazing-feature`
4. Make your changes
5. Commit with clear messages: `git commit -m 'Add amazing feature'`
6. Push to your branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## Development Setup

See [DEVELOPMENT.md](DEVELOPMENT.md) for detailed development environment setup and guidelines.

## Code Style

- Follow TypeScript best practices
- Use ESLint: `pnpm lint`
- Format code consistently with the existing codebase
- Write meaningful commit messages

## Pull Request Process

1. Update documentation if needed
2. Ensure all tests pass
3. Update the README.md if you add new features
4. Reference any related issues in your PR description
5. Wait for review from maintainers

## Reporting Bugs

- Use GitHub Issues
- Describe the bug clearly
- Include steps to reproduce
- Share relevant logs or screenshots
- Mention your environment (OS, Node version, etc.)

## Feature Requests

- Open a GitHub Issue
- Describe the feature and its use case
- Explain why it would benefit the project
- Be open to discussion and feedback

## Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help create a welcoming environment
- Report unacceptable behavior to maintainers

## Questions?

Feel free to open an issue for questions or discussion!
