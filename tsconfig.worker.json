{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ES2022", "lib": ["ES2022"], "types": ["@cloudflare/workers-types"], "moduleResolution": "bundler", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "noEmit": true, "isolatedModules": true}, "include": ["worker/**/*"], "exclude": ["node_modules", "dist"]}