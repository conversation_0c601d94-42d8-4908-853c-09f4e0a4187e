name = "focal-expensi"
main = "worker/index.ts"
compatibility_date = "2025-10-04"
compatibility_flags = ["nodejs_compat"]

# Serve frontend assets (production only)
# For local development, Vite serves the frontend on port 3000
[assets]
directory = "./dist"
binding = "ASSETS"

# D1 Database binding
[[d1_databases]]
binding = "DB"
database_name = "focal_expensi_db"
database_id = "85a073b1-3c48-4a21-bf35-68e7c473d654"

# Environment variables (use wrangler secrets for production)
[vars]
NODE_ENV = "production"

# Production secrets (set using wrangler secret put):
# JWT_SECRET - Secret key for JWT token signing
# ENCRYPTION_KEY - Key for encrypting API keys in database
# BREVO_API_KEY - (Optional) Brevo API key for sending transactional emails
# APP_URL - (Optional) Application URL for email verification links (e.g., https://focal-expense.app)
